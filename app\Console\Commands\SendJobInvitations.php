<?php

namespace App\Console\Commands;

use App\Jobs\SendJobInvitationEmail;
use App\Models\Job;
use App\Models\JobInvitationLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SendJobInvitations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'job:send-invitations 
                            {--limit=100 : Số lượng ứng viên tối đa được xử lý}
                            {--dry-run : Chạy thử không gửi email thật}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gửi email mời ứng tuyển cho ứng viên phù hợp với job';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $limit = $this->option('limit');
        $isDryRun = $this->option('dry-run');

        $this->info('Bắt đầu tìm kiếm ứng viên phù hợp...');

        // Lấy danh sách ứng viên đã đăng nhập trong 2 tháng gần đây
        $twoMonthsAgo = Carbon::now()->subMonths(24);

        $candidates = User::where(function ($query) use ($twoMonthsAgo) {
            $query->where('last_login_at', '>=', $twoMonthsAgo)
                ->orWhere('created_at', '>=', $twoMonthsAgo);
        })
            ->where('email_verified_at', '!=', null)
            ->where('is_active', ACTIVE)
            ->whereHas('search_cv') // Chỉ lấy user có profile trong search_cv
            ->limit($limit)
            ->get();

        $this->info("Tìm thấy {$candidates->count()} ứng viên đủ điều kiện.");

        $totalEmailsSent = 0;
        $totalCandidatesProcessed = 0;

        foreach ($candidates as $candidate) {
            $matchingJobs = $this->findMatchingJobs($candidate);

            if ($matchingJobs->isEmpty()) {
                continue;
            }

            // Lọc ra những job chưa gửi hoặc đã gửi dưới 2 lần
            $jobsToSend = collect();
            foreach ($matchingJobs as $job) {
                $invitationLog = JobInvitationLog::where('user_id', $candidate->id)
                    ->where('job_id', $job->id)
                    ->first();

                if (!$invitationLog || $invitationLog->sent_count < 2) {
                    $jobsToSend->push($job);
                }
            }

            if ($jobsToSend->isEmpty()) {
                continue;
            }

            $this->info("Ứng viên: {$candidate->name} ({$candidate->email}) - {$jobsToSend->count()} job phù hợp");

            if (!$isDryRun) {
                // Dispatch job to send email
                dispatch(new SendJobInvitationEmail($candidate, $jobsToSend));
                $totalEmailsSent++;
                // dd($totalEmailsSent);
            } else {
                $this->line("  [DRY RUN] Sẽ gửi email với các job:");
                foreach ($jobsToSend as $job) {
                    $matchScore = isset($job->job_title_match_score) ? round($job->job_title_match_score, 2) : 0;
                    $this->line("    - {$job->name} ({$job->company->name}) [Match score: {$matchScore}]");
                }
            }

            $totalCandidatesProcessed++;
        }

        $this->info("Hoàn thành! Đã xử lý {$totalCandidatesProcessed} ứng viên.");
        if (!$isDryRun) {
            $this->info("Đã gửi {$totalEmailsSent} email.");
        }

        return Command::SUCCESS;
    }

    /**
     * Tìm các job phù hợp với ứng viên
     */
    private function findMatchingJobs($candidate)
    {
        $searchCv = $candidate->search_cv;
        if (!$searchCv) {
            return collect();
        }

        // Build base query
        $query = Job::active()
            ->real()
            ->with(['company', 'addresses', 'career_level', 'skill'])
            // ->whereIn('status_id', [5, 27]) // Đã kích hoạt , Chờ hậu kiểm
            ->where('expired_at', '>=', now());

        // Add fulltext matching score for job title if candidate has job_title
        if (!empty($searchCv->job_title)) {
            $escapedJobTitle = DB::connection()->getPdo()->quote($searchCv->job_title);
            $query->selectRaw("jobs.*, IFNULL(MATCH(jobs.name) AGAINST ({$escapedJobTitle} IN NATURAL LANGUAGE MODE), 0) as job_title_match_score");
        } else {
            $query->selectRaw("jobs.*, 0 as job_title_match_score");
        }

        // Matching theo kỹ năng
        // if ($searchCv->skill_ids) {
        //     $skillIds = json_decode($searchCv->skill_ids, true) ?: [];
        //     if (!empty($skillIds)) {
        //         $query->whereHas('skill', function ($q) use ($skillIds) {
        //             $q->whereIn('skills.id', $skillIds);
        //         });
        //     }
        // }

        // Matching theo cấp bậc
        // if ($searchCv->career_level_ids) {
        //     $careerLevelIds = json_decode($searchCv->career_level_ids, true) ?: [];
        //     if (!empty($careerLevelIds)) {
        //         $query->whereHas('career_level', function ($q) use ($careerLevelIds) {
        //             $q->whereIn('career_levels.id', $careerLevelIds);
        //         });
        //     }
        // }

        // Matching theo địa điểm (province)
        if ($searchCv->location) {
            $location = $searchCv->location == 'ho-chi-minh' ? 2 : 1;
            $query->whereHas('addresses', function ($q) use ($location) {
                $q->where('province_id', $location);
            });
        }

        // // Matching theo mức lương kỳ vọng
        // if ($searchCv->salary_expect_min && $searchCv->salary_expect_max) {
        //     $query->where(function ($q) use ($searchCv) {
        //         $q->where('salary_max', '>=', $searchCv->salary_expect_min)
        //             ->orWhere('salary_is_negotiate', 1);
        //     });
        // }

        // Order by job title match score first, then by published date
        // Jobs with higher fulltext match score will be prioritized
        // dd($query->having('job_title_match_score', '>', 0)
        //     ->orderByDesc('job_title_match_score')
        //     ->orderBy('published_at', 'desc')
        //     ->limit(5)->toSql());
        return $query->having('job_title_match_score', '>', 0)
            ->orderByDesc('job_title_match_score')
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();
    }
}
